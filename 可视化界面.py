import tkinter as tk
from tkinter import filedialog, messagebox, ttk, scrolledtext
from PIL import Image, ImageTk
import numpy as np
import torch
import torch.nn as nn
from torchvision import transforms, models
import os
import threading
from datetime import datetime

# Import LLM modules
from typing import Optional
import requests

class ColonCancerModel(nn.Module):
    """PyTorch Colon Cancer Cell Classification Model"""

    def __init__(self, num_classes=1):
        super(ColonCancerModel, self).__init__()
        # Use pre-trained VGG16 as backbone model
        self.backbone = models.vgg16(pretrained=True)

        # Freeze feature extraction layer weights
        for param in self.backbone.features.parameters():
            param.requires_grad = False

        # Replace classifier
        self.backbone.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(512 * 7 * 7, 4096),
            nn.ReLU(True),
            nn.Dropout(0.5),
            nn.Linear(4096, 128),
            nn.<PERSON><PERSON><PERSON>(True),
            nn.Dropout(0.3),
            nn.<PERSON>ar(128, 64),
            nn.<PERSON><PERSON><PERSON>(True),
            nn.Dropout(0.2),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        # Through feature extractor
        x = self.backbone.features(x)
        # Global average pooling
        x = self.backbone.avgpool(x)
        # Flatten
        x = torch.flatten(x, 1)
        # Through classifier
        x = self.backbone.classifier(x)
        return x

class QianwenChat:
    """Qianwen AI Chat Client"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def generate_medical_report(self, prediction_result: str, confidence: float,
                              image_features: str = "") -> Optional[str]:
        """Generate medical diagnostic report"""

        prompt = f"""
As a professional pathologist, please generate a detailed medical diagnostic report based on the following colon cell detection results:

Detection Result: {prediction_result}
Confidence Level: {confidence:.2%}
Image Features: {image_features}

Please generate a professional medical report containing the following sections:
1. Detection Result Summary
2. Pathological Analysis
3. Risk Assessment
4. Recommended Follow-up Examinations or Treatment Plans
5. Important Notes

The report should be professional, accurate, and easy to understand, suitable for clinical physician reference.
"""

        data = {
            "model": "qwen-turbo",
            "input": {
                "messages": [{"role": "user", "content": prompt}]
            },
            "parameters": {
                "max_tokens": 2000,
                "temperature": 0.3,
                "top_p": 0.8
            }
        }

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("output") and result["output"].get("text"):
                    return result["output"]["text"]
            return None
        except Exception as e:
            print(f"LLM report generation error: {e}")
            return None

class ColonCancerDetectorUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Intelligent Colon Cancer Cell Detection and Diagnosis System")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f8f9fa')
        # Center window display
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

        # Model related
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.img_size = (224, 224)
        self.class_names = ['Cancer Cell', 'Benign Cell']

        # Data preprocessing
        self.transform = transforms.Compose([
            transforms.Resize(self.img_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # Current image
        self.current_image = None
        self.current_image_path = None
        self.current_prediction = None
        self.current_confidence = None

        # LLM client
        self.llm_client = QianwenChat("sk-59b3f012029d4fa9965b999d7fb00b4f")

        # Create styles
        self.setup_styles()
        self.setup_ui()
        self.load_model()

    def setup_styles(self):
        """Setup modern styles"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # Configure styles
        self.style.configure('Title.TLabel',
                           font=('Arial', 24, 'bold'),
                           foreground='#2c3e50',
                           background='#f8f9fa')

        self.style.configure('Heading.TLabel',
                           font=('Arial', 14, 'bold'),
                           foreground='#34495e',
                           background='#f8f9fa')

        self.style.configure('Info.TLabel',
                           font=('Arial', 11),
                           foreground='#7f8c8d',
                           background='#f8f9fa')

        # Button styles
        self.style.configure('Primary.TButton',
                           font=('Arial', 12, 'bold'),
                           padding=(20, 10))

        self.style.configure('Success.TButton',
                           font=('Arial', 12, 'bold'),
                           padding=(20, 10))

        self.style.configure('Danger.TButton',
                           font=('Arial', 12, 'bold'),
                           padding=(20, 10))

    def setup_ui(self):
        """Setup modern user interface"""
        # Main container
        main_container = tk.Frame(self.root, bg='#f8f9fa')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Title area
        title_frame = tk.Frame(main_container, bg='#f8f9fa')
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = ttk.Label(
            title_frame,
            text="🔬 Intelligent Colon Cancer Cell Detection and Diagnosis System",
            style='Title.TLabel'
        )
        title_label.pack()

        subtitle_label = ttk.Label(
            title_frame,
            text="Deep Learning-based Medical Image Analysis and AI-assisted Diagnosis",
            style='Info.TLabel'
        )
        subtitle_label.pack(pady=(5, 0))

        # Main content area - using PanedWindow for splitting
        main_paned = ttk.PanedWindow(main_container, orient='horizontal')
        main_paned.pack(fill='both', expand=True)

        # Left panel - image and controls
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=3)  # Increase left weight for more image space

        # Control button area - modern design
        control_frame = ttk.LabelFrame(left_frame, text="🎛️ Operation Control Panel", padding=20)
        control_frame.pack(fill='x', pady=(0, 15))

        # Create button container using grid layout
        button_container = tk.Frame(control_frame, bg='#f8f9fa')
        button_container.pack(fill='x')

        # Button style configuration
        button_style = {
            'font': ("Arial", 11, "bold"),
            'cursor': 'hand2',
            'relief': 'flat',
            'borderwidth': 0,
            'padx': 20,
            'pady': 15,
            'width': 12,
            'activebackground': '#34495e',
            'activeforeground': 'white'
        }

        # First row buttons
        top_row = tk.Frame(button_container, bg='#f8f9fa')
        top_row.pack(fill='x', pady=(0, 10))

        # Upload button - blue theme
        self.upload_btn = tk.Button(
            top_row,
            text="📁 Upload Image",
            command=self.upload_image,
            bg='#3498db',
            fg='white',
            **button_style
        )
        self.upload_btn.pack(side=tk.LEFT, padx=(0, 10), expand=True, fill='x')

        # Detection button - red theme
        self.detect_btn = tk.Button(
            top_row,
            text="🔍 Start Detection",
            command=self.detect_cancer,
            bg='#e74c3c',
            fg='white',
            state='disabled',
            **button_style
        )
        self.detect_btn.pack(side=tk.LEFT, padx=(5, 0), expand=True, fill='x')

        # Second row buttons
        bottom_row = tk.Frame(button_container, bg='#f8f9fa')
        bottom_row.pack(fill='x')

        # Generate report button - green theme
        self.report_btn = tk.Button(
            bottom_row,
            text="📋 Generate Report",
            command=self.generate_report,
            bg='#27ae60',
            fg='white',
            state='disabled',
            **button_style
        )
        self.report_btn.pack(side=tk.LEFT, padx=(0, 10), expand=True, fill='x')

        # Clear button - gray theme
        self.clear_btn = tk.Button(
            bottom_row,
            text="🗑️ Clear Reset",
            command=self.clear_results,
            bg='#95a5a6',
            fg='white',
            **button_style
        )
        self.clear_btn.pack(side=tk.LEFT, padx=(5, 0), expand=True, fill='x')

        # Add button hover effects
        self._setup_button_hover_effects()

        # Image display area
        image_frame = ttk.LabelFrame(left_frame, text="Medical Image", padding=15)
        image_frame.pack(fill='both', expand=True)

        # Image container - set fixed minimum size
        image_container = tk.Frame(image_frame, bg='white', relief='sunken', bd=2)
        image_container.pack(fill='both', expand=True, padx=5, pady=5)
        image_container.configure(width=550, height=450)  # Set minimum size
        image_container.pack_propagate(False)  # Prevent container shrinking

        self.image_label = tk.Label(
            image_container,
            text="Please upload colon cell microscopy image\n\nSupported formats: JPG, PNG, BMP, TIFF\nRecommended resolution: 224x224 pixels or higher",
            font=("Arial", 12),
            bg='white',
            fg='#7f8c8d',
            justify='center'
        )
        self.image_label.pack(expand=True, fill='both')

        # Right panel - results and reports
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)

        # Detection results area
        result_frame = ttk.LabelFrame(right_frame, text="Detection Results", padding=20)
        result_frame.pack(fill='x', pady=(0, 20))

        self.result_label = tk.Label(
            result_frame,
            text="Waiting for detection...",
            font=("Arial", 16, "bold"),
            bg='#f8f9fa',
            fg='#7f8c8d'
        )
        self.result_label.pack(pady=(0, 10))

        self.confidence_label = tk.Label(
            result_frame,
            text="",
            font=("Arial", 12),
            bg='#f8f9fa',
            fg='#7f8c8d'
        )
        self.confidence_label.pack()

        # AI diagnostic report area
        report_frame = ttk.LabelFrame(right_frame, text="AI Intelligent Diagnostic Report", padding=20)
        report_frame.pack(fill='both', expand=True)

        # Report text area
        self.report_text = scrolledtext.ScrolledText(
            report_frame,
            wrap=tk.WORD,
            font=("Arial", 11),
            bg='white',
            fg='#2c3e50',
            relief='flat',
            borderwidth=1,
            state='disabled'
        )
        self.report_text.pack(fill='both', expand=True)

        # Bottom status bar
        status_frame = tk.Frame(main_container, bg='#f8f9fa')
        status_frame.pack(fill='x', pady=(20, 0))

        # Progress bar
        self.progress = ttk.Progressbar(
            status_frame,
            mode='indeterminate',
            length=400
        )

        # Status label
        self.status_label = tk.Label(
            status_frame,
            text="System ready - Please upload medical image to start detection",
            font=("Arial", 10),
            bg='#f8f9fa',
            fg='#27ae60'
        )
        self.status_label.pack(side=tk.LEFT)

    def _setup_button_hover_effects(self):
        """Setup button hover effects"""
        # Define hover colors
        hover_colors = {
            self.upload_btn: ('#2980b9', '#3498db'),      # Dark blue -> Light blue
            self.detect_btn: ('#c0392b', '#e74c3c'),      # Dark red -> Light red
            self.report_btn: ('#229954', '#27ae60'),      # Dark green -> Light green
            self.clear_btn: ('#7f8c8d', '#95a5a6')        # Dark gray -> Light gray
        }

        for button, (hover_color, normal_color) in hover_colors.items():
            # Bind mouse enter event
            button.bind("<Enter>", lambda _, btn=button, color=hover_color:
                       btn.config(bg=color) if btn['state'] != 'disabled' else None)

            # Bind mouse leave event
            button.bind("<Leave>", lambda _, btn=button, color=normal_color:
                       btn.config(bg=color) if btn['state'] != 'disabled' else None)

    def load_model(self):
        """Load trained PyTorch model"""
        try:
            # Try to load PyTorch model
            model_path = 'colon_cancer_classifier.pth'
            backup_path = 'best_colon_cancer_model.pth'

            # Create model instance
            self.model = ColonCancerModel(num_classes=1)

            if os.path.exists(model_path):
                self.model.load_state_dict(torch.load(model_path, map_location=self.device))
                self.model.to(self.device)
                self.model.eval()
                self.status_label.config(text="PyTorch model loaded successfully", fg='#27ae60')
            elif os.path.exists(backup_path):
                self.model.load_state_dict(torch.load(backup_path, map_location=self.device))
                self.model.to(self.device)
                self.model.eval()
                self.status_label.config(text="Backup PyTorch model loaded successfully", fg='#27ae60')
            else:
                # Try to load old Keras model files (compatibility)
                keras_path = 'colon_cancer_classifier.h5'
                keras_backup = 'best_colon_cancer_model.h5'

                if os.path.exists(keras_path) or os.path.exists(keras_backup):
                    self.model = None
                    self.status_label.config(text="Keras model detected, please convert to PyTorch format first", fg='#f39c12')
                    messagebox.showwarning(
                        "Model Format Notice",
                        "Old Keras model file detected!\nPlease run the new PyTorch version training script to generate .pth model file."
                    )
                else:
                    self.model = None
                    self.status_label.config(text="Model file not found, please train model first", fg='#e74c3c')
                    messagebox.showwarning(
                        "Warning",
                        "No trained model file found!\nPlease run the training script to generate PyTorch model (.pth file) first."
                    )
        except Exception as e:
            self.model = None
            self.status_label.config(text=f"Model loading failed: {str(e)}", fg='#e74c3c')
            messagebox.showerror("Error", f"Model loading failed: {str(e)}")
    
    def upload_image(self):
        """Upload image"""
        file_types = [
            ("Image Files", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG Files", "*.jpg *.jpeg"),
            ("PNG Files", "*.png"),
            ("All Files", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="Select Image File",
            filetypes=file_types
        )

        if file_path:
            try:
                # Load and display image
                self.current_image_path = file_path
                self.display_image(file_path)
                self.detect_btn.config(state='normal')
                self.status_label.config(text="Image uploaded successfully", fg='#27ae60')

                # Clear previous results
                self.result_label.config(text="")
                self.confidence_label.config(text="")

            except Exception as e:
                messagebox.showerror("Error", f"Cannot load image: {str(e)}")
                self.status_label.config(text="Image loading failed", fg='#e74c3c')

    def display_image(self, image_path):
        """Display image - support larger sizes"""
        try:
            # Load image
            image = Image.open(image_path)

            # Set larger display size
            max_width = 500
            max_height = 400

            # Get original image dimensions
            orig_width, orig_height = image.size

            # Calculate scaling ratio while maintaining aspect ratio
            width_ratio = max_width / orig_width
            height_ratio = max_height / orig_height
            scale_ratio = min(width_ratio, height_ratio)

            # If image is smaller than target size, scale up appropriately
            if scale_ratio < 1:
                new_width = int(orig_width * scale_ratio)
                new_height = int(orig_height * scale_ratio)
            else:
                # If image is very small, scale up appropriately
                scale_ratio = min(2.0, min(max_width / orig_width, max_height / orig_height))
                new_width = int(orig_width * scale_ratio)
                new_height = int(orig_height * scale_ratio)

            # Resize image
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(image)

            # Update label
            self.image_label.config(image=photo, text="", compound='center')
            self.image_label.image = photo  # Keep reference

        except Exception as e:
            messagebox.showerror("Error", f"Cannot display image: {str(e)}")
    
    def detect_cancer(self):
        """Detect cancer cells - PyTorch version"""
        if self.model is None:
            messagebox.showerror("Error", "Model not loaded, cannot perform detection")
            return

        if self.current_image_path is None:
            messagebox.showerror("Error", "Please upload image first")
            return

        try:
            # Show progress bar
            self.progress.pack(pady=10)
            self.progress.start()
            self.status_label.config(text="🔍 Performing AI intelligent detection analysis...", fg='#f39c12')
            self.root.update()

            # Preprocess image - using PIL and torchvision
            img = Image.open(self.current_image_path).convert('RGB')
            img_tensor = self.transform(img).unsqueeze(0).to(self.device)

            # Perform prediction
            self.model.eval()
            with torch.no_grad():
                output = self.model(img_tensor)
                prediction_prob = torch.sigmoid(output).cpu().numpy()[0][0]

            # Binary classification: 0=cancer cell, 1=benign cell
            predicted_class = 0 if prediction_prob < 0.5 else 1
            confidence = 1 - prediction_prob if predicted_class == 0 else prediction_prob

            # Save prediction results
            self.current_prediction = self.class_names[predicted_class]
            self.current_confidence = confidence

            # Stop progress bar
            self.progress.stop()
            self.progress.pack_forget()

            # Display results
            class_name = self.class_names[predicted_class]

            # Set result color and icon
            if predicted_class == 0:  # Cancer cell
                result_color = '#e74c3c'
                status_text = "⚠️ Cancer cell features detected"
                result_icon = "🔴"
            else:  # Benign
                result_color = '#27ae60'
                status_text = "✅ Benign cell features detected"
                result_icon = "🟢"

            self.result_label.config(
                text=f"{result_icon} Detection Result: {class_name}",
                fg=result_color
            )

            self.confidence_label.config(
                text=f"🎯 Confidence: {confidence:.2%} | 📊 Accuracy: {'High' if confidence > 0.8 else 'Medium' if confidence > 0.6 else 'Low'}"
            )

            self.status_label.config(text=status_text, fg=result_color)

            # Enable report generation button
            self.report_btn.config(state='normal')

            # Display preliminary results in report area
            self.update_report_preview(class_name, confidence, predicted_class)

        except Exception as e:
            # Stop progress bar
            self.progress.stop()
            self.progress.pack_forget()

            messagebox.showerror("Error", f"Detection failed: {str(e)}")
            self.status_label.config(text="❌ Detection failed", fg='#e74c3c')

    def update_report_preview(self, class_name, confidence, predicted_class):
        """Update report preview"""
        self.report_text.config(state='normal')
        self.report_text.delete(1.0, tk.END)

        preview_text = f"""📋 Detection Results Preview
{'='*50}

🔬 Detection Result: {class_name}
🎯 Confidence: {confidence:.2%}
📅 Detection Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{'⚠️ Risk Alert:' if predicted_class == 0 else '✅ Preliminary Assessment:'}
{('Suspected cancer cell features detected. Immediate further medical examination and pathological confirmation recommended.' if predicted_class == 0 else 'Detection results show benign cell features, but regular follow-up examinations are still recommended.')}

💡 Tip: Click "Generate Report" button to get AI-generated detailed medical diagnostic report
"""

        self.report_text.insert(tk.END, preview_text)
        self.report_text.config(state='disabled')

    def generate_report(self):
        """Generate detailed AI diagnostic report"""
        if self.current_prediction is None or self.current_confidence is None:
            messagebox.showwarning("Warning", "Please perform detection first")
            return

        # Generate report in new thread to avoid UI freezing
        threading.Thread(target=self._generate_report_async, daemon=True).start()

    def _generate_report_async(self):
        """Generate report asynchronously"""
        try:
            # Update status
            self.root.after(0, lambda: self.status_label.config(
                text="🤖 AI is generating detailed diagnostic report...", fg='#f39c12'))
            self.root.after(0, lambda: self.progress.pack(pady=10))
            self.root.after(0, lambda: self.progress.start())

            # Generate image feature description
            image_features = self._analyze_image_features()

            # Call LLM to generate report
            report = self.llm_client.generate_medical_report(
                self.current_prediction,
                self.current_confidence,
                image_features
            )

            # Update UI
            self.root.after(0, lambda: self._update_report_ui(report))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Report generation failed: {str(e)}"))
            self.root.after(0, lambda: self.status_label.config(text="❌ Report generation failed", fg='#e74c3c'))
        finally:
            self.root.after(0, lambda: self.progress.stop())
            self.root.after(0, lambda: self.progress.pack_forget())

    def _analyze_image_features(self):
        """Analyze image features - using PIL"""
        try:
            # Load image using PIL
            img = Image.open(self.current_image_path).convert('RGB')
            img_array = np.array(img)

            # Convert to grayscale
            img_gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])

            # Basic image statistics
            mean_intensity = np.mean(img_gray)
            std_intensity = np.std(img_gray)

            # Texture analysis
            contrast = np.std(img_gray)

            # Image dimension information
            height, width = img_gray.shape

            features = f"Image dimensions: {width}x{height}, Mean brightness: {mean_intensity:.2f}, Standard deviation: {std_intensity:.2f}, Contrast: {contrast:.2f}"
            return features
        except Exception as e:
            return f"Image feature analysis unavailable: {str(e)}"

    def _update_report_ui(self, report):
        """Update report UI"""
        self.report_text.config(state='normal')
        self.report_text.delete(1.0, tk.END)

        if report:
            # Add report header
            header = f"""🏥 AI Intelligent Diagnostic Report
{'='*60}
📅 Generation Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔬 Detection Result: {self.current_prediction}
🎯 Confidence Level: {self.current_confidence:.2%}
{'='*60}

"""
            self.report_text.insert(tk.END, header)
            self.report_text.insert(tk.END, report)

            # Add disclaimer
            disclaimer = f"""

{'='*60}
⚠️ Important Disclaimer:
This report is generated by an AI system and is for medical reference only. It cannot replace professional physician diagnosis.
Final diagnostic results should be based on pathological examination by professional medical institutions.
Further examination and treatment are recommended under the guidance of professional physicians.
{'='*60}
"""
            self.report_text.insert(tk.END, disclaimer)

            self.status_label.config(text="✅ AI diagnostic report generation completed", fg='#27ae60')
        else:
            self.report_text.insert(tk.END, "❌ Report generation failed, please check network connection or try again later")
            self.status_label.config(text="❌ Report generation failed", fg='#e74c3c')

        self.report_text.config(state='disabled')
    
    def clear_results(self):
        """Clear all results"""
        self.current_image_path = None
        self.current_prediction = None
        self.current_confidence = None

        # Clear image
        self.image_label.config(
            image="",
            text="Please upload colon cell microscopy image\n\nSupported formats: JPG, PNG, BMP, TIFF\nRecommended resolution: 224x224 pixels or higher"
        )
        self.image_label.image = None

        # Clear results
        self.result_label.config(text="Waiting for detection...", fg='#7f8c8d')
        self.confidence_label.config(text="")

        # Clear report
        self.report_text.config(state='normal')
        self.report_text.delete(1.0, tk.END)
        self.report_text.config(state='disabled')

        # Reset button states
        self.detect_btn.config(state='disabled')
        self.report_btn.config(state='disabled')

        # Reset status
        self.status_label.config(text="System ready - Please upload medical image to start detection", fg='#27ae60')


def main():
    """Main function"""
    root = tk.Tk()
    ColonCancerDetectorUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
