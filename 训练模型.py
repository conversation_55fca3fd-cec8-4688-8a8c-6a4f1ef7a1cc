#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结肠癌细胞分类器
使用深度学习对结肠癌细胞图像进行分类 (PyTorch版本)
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, roc_curve, auc, precision_recall_curve
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import transforms, models
from torchvision.datasets import ImageFolder
import pickle
import warnings
from PIL import Image
warnings.filterwarnings('ignore')

# 尝试导入SHAP，如果失败则设置标志
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError as e:
    print(f"警告: SHAP库导入失败 ({e})，将跳过SHAP分析")
    SHAP_AVAILABLE = False

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 确保输出编码为UTF-8
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')

class ColonCancerModel(nn.Module):
    """PyTorch结肠癌细胞分类模型"""

    def __init__(self, num_classes=1):
        super(ColonCancerModel, self).__init__()
        # 使用预训练的VGG16作为基础模型
        self.backbone = models.vgg16(pretrained=True)

        # 冻结特征提取层的权重
        for param in self.backbone.features.parameters():
            param.requires_grad = False

        # 获取VGG16特征提取器的输出维度
        # VGG16的features输出是 [batch_size, 512, 7, 7] (对于224x224输入)

        # 替换分类器
        self.backbone.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(512 * 7 * 7, 4096),
            nn.ReLU(True),
            nn.Dropout(0.5),
            nn.Linear(4096, 128),
            nn.ReLU(True),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(True),
            nn.Dropout(0.2),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        # 通过特征提取器
        x = self.backbone.features(x)
        # 全局平均池化
        x = self.backbone.avgpool(x)
        # 展平
        x = torch.flatten(x, 1)
        # 通过分类器
        x = self.backbone.classifier(x)
        return x

def set_random_seeds(seed=42):
    """设置所有随机种子以确保可重现性"""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    # 确保CUDA操作的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"已设置随机种子: {seed}")

class ColonCancerClassifier:
    """Colon Cancer Cell Classifier Class"""

    def __init__(self, img_size=(224, 224), batch_size=32, random_seed=42):
        """
        Initialize classifier

        Args:
            img_size: Image size
            batch_size: Batch size
            random_seed: Random seed
        """
        # Set random seed
        set_random_seeds(random_seed)

        self.img_size = img_size
        self.batch_size = batch_size
        self.random_seed = random_seed
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.history = {'train_loss': [], 'train_acc': [], 'test_loss': [], 'test_acc': []}
        self.class_names = ['colon_aca', 'colon_n']  # Cancer cells, Normal cells
        print(f"Using device: {self.device}")
        print(f"Random seed: {random_seed}")

    def create_model(self):
        """Create CNN model"""
        self.model = ColonCancerModel(num_classes=1)
        self.model.to(self.device)
        return self.model

    def get_transforms(self, is_training=True):
        """Get data transforms"""
        # Use same transforms for training and testing to ensure fair comparison
        return transforms.Compose([
            transforms.Resize((self.img_size[0], self.img_size[1])),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

    def prepare_data(self, data_dir='colon_dataset_split', random_seed=None):
        """
        Prepare training and testing data

        Args:
            data_dir: Dataset directory
            random_seed: Random seed, if None use instance random seed
        """
        if random_seed is None:
            random_seed = self.random_seed

        train_dir = os.path.join(data_dir, 'train')
        test_dir = os.path.join(data_dir, 'test')

        # Create datasets
        train_dataset = ImageFolder(train_dir, transform=self.get_transforms(is_training=True))
        test_dataset = ImageFolder(test_dir, transform=self.get_transforms(is_training=False))

        print(f"Data split information:")
        print(f"  Training set: {len(train_dataset)} samples (complete training set)")
        print(f"  Test set: {len(test_dataset)} samples (independent test set)")

        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True, num_workers=4)
        test_loader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False, num_workers=4)

        return train_loader, test_loader
    
    def train_epoch(self, train_loader, criterion, optimizer):
        """训练一个epoch"""
        self.model.train()
        running_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device).float()

            optimizer.zero_grad()
            output = self.model(data).squeeze()
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()

            running_loss += loss.item()
            predicted = (torch.sigmoid(output) > 0.5).float()
            total += target.size(0)
            correct += (predicted == target).sum().item()

            if batch_idx % 10 == 0:
                print(f'Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')

        epoch_loss = running_loss / len(train_loader)
        epoch_acc = correct / total
        return epoch_loss, epoch_acc

    def validate_epoch(self, val_loader, criterion):
        """验证一个epoch"""
        self.model.eval()
        running_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(self.device), target.to(self.device).float()
                output = self.model(data).squeeze()
                loss = criterion(output, target)

                running_loss += loss.item()
                predicted = (torch.sigmoid(output) > 0.5).float()
                total += target.size(0)
                correct += (predicted == target).sum().item()

        epoch_loss = running_loss / len(val_loader)
        epoch_acc = correct / total
        return epoch_loss, epoch_acc

    def train(self, epochs=50, data_dir='colon_dataset_split'):
        """
        训练模型

        Args:
            epochs: 训练轮数
            data_dir: 数据集目录
        """
        print("开始准备数据...")
        train_loader, test_loader = self.prepare_data(data_dir)

        print("创建模型...")
        self.create_model()

        print("模型结构:")
        print(self.model)

        # 设置损失函数和优化器
        criterion = nn.BCEWithLogitsLoss()
        optimizer = optim.Adam(self.model.parameters(), lr=0.0001)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, min_lr=1e-7)

        # 早停参数 - 基于训练损失
        best_train_loss = float('inf')
        patience = 10
        patience_counter = 0

        print("开始训练...")
        for epoch in range(epochs):
            print(f'\nEpoch {epoch+1}/{epochs}')
            print('-' * 50)

            # 训练
            train_loss, train_acc = self.train_epoch(train_loader, criterion, optimizer)

            # 在测试集上评估（不参与训练，仅用于监控）
            test_loss, test_acc = self.validate_epoch(test_loader, criterion)

            # 学习率调度 - 基于训练损失
            scheduler.step(train_loss)

            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['train_acc'].append(train_acc)
            self.history['test_loss'].append(test_loss)
            self.history['test_acc'].append(test_acc)

            print(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}')
            print(f'Test Loss: {test_loss:.4f}, Test Acc: {test_acc:.4f}')

            # 保存最佳模型 - 基于训练损失（不使用测试集损失避免数据泄露）
            if train_loss < best_train_loss:
                best_train_loss = train_loss
                patience_counter = 0
                torch.save(self.model.state_dict(), 'best_colon_cancer_model.pth')
                print('保存最佳模型')
            else:
                patience_counter += 1

            # 早停 - 基于训练损失（不使用测试集避免数据泄露）
            if patience_counter >= patience:
                print(f'早停在epoch {epoch+1}')
                break

        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_colon_cancer_model.pth'))

        # 保存最终模型
        torch.save(self.model.state_dict(), 'colon_cancer_classifier.pth')
        print("模型已保存为 colon_cancer_classifier.pth")

        # 保存训练历史
        with open('training_history.pkl', 'wb') as f:
            pickle.dump(self.history, f)

        # 在测试集上评估
        print("\n在测试集上评估模型...")
        test_loss, test_accuracy = self.validate_epoch(test_loader, criterion)
        print(f"测试集损失: {test_loss:.4f}")
        print(f"测试集准确率: {test_accuracy:.4f}")

        # 绘制训练历史
        self.plot_training_history()

        # 进行完整的测试集分析
        print("\n开始进行完整的模型分析...")
        self.comprehensive_analysis(test_loader)

        return self.history
    
    def plot_training_history(self):
        """绘制训练历史"""
        if not self.history or not self.history['train_loss']:
            print("没有训练历史可绘制")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 绘制准确率对比
        ax1.plot(self.history['train_acc'], label='训练准确率', color='blue', linewidth=2)
        if 'test_acc' in self.history and self.history['test_acc']:
            ax1.plot(self.history['test_acc'], label='测试准确率', color='green', linewidth=2)
        ax1.set_title('模型准确率对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('轮次')
        ax1.set_ylabel('准确率')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1)

        # 绘制损失对比
        ax2.plot(self.history['train_loss'], label='训练损失', color='red', linewidth=2)
        if 'test_loss' in self.history and self.history['test_loss']:
            ax2.plot(self.history['test_loss'], label='测试损失', color='orange', linewidth=2)
        ax2.set_title('模型损失对比', fontsize=14, fontweight='bold')
        ax2.set_xlabel('轮次')
        ax2.set_ylabel('损失')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("训练历史图已保存为 training_history.png")

    def load_model(self, model_path):
        """
        加载已训练的模型

        Args:
            model_path: 模型文件路径
        """
        if os.path.exists(model_path):
            if self.model is None:
                self.create_model()
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.eval()
            print(f"模型已从 {model_path} 加载")
            return True
        else:
            print(f"模型文件 {model_path} 不存在")
            return False
    
    def predict_image(self, image_path):
        """
        预测单张图像

        Args:
            image_path: 图像文件路径
        """
        if self.model is None:
            print("请先加载模型")
            return None

        # 读取和预处理图像
        img = Image.open(image_path).convert('RGB')
        transform = self.get_transforms(is_training=False)
        img_tensor = transform(img).unsqueeze(0).to(self.device)

        # 预测
        self.model.eval()
        with torch.no_grad():
            output = self.model(img_tensor)
            prediction = torch.sigmoid(output).item()

        # 解释结果
        if prediction > 0.5:
            result = "正常细胞"
            confidence = prediction
        else:
            result = "癌细胞"
            confidence = 1 - prediction

        return {
            'prediction': result,
            'confidence': confidence,
            'raw_score': prediction
        }
    
    def load_data_split(self, split_file='data_split.pkl'):
        """加载数据划分信息"""
        if os.path.exists(split_file):
            with open(split_file, 'rb') as f:
                self.data_split = pickle.load(f)
            return True
        return False

    def comprehensive_analysis(self, test_loader=None):
        """
        进行全面的模型分析，包括混淆矩阵、SHAP分析和多种可视化

        Args:
            test_loader: 测试数据加载器，如果为None则自动创建
        """
        if self.model is None:
            print("❌ 没有可用的模型进行分析")
            return

        print("正在进行全面的模型分析...")

        # 如果没有提供测试加载器，则创建一个
        if test_loader is None:
            print("创建测试数据加载器...")
            _, test_loader = self.prepare_data()

        # 获取测试数据的预测结果
        self.model.eval()
        y_pred_proba = []
        y_true = []

        with torch.no_grad():
            for data, target in test_loader:
                data = data.to(self.device)
                output = self.model(data)
                proba = torch.sigmoid(output).cpu().numpy()
                y_pred_proba.extend(proba.flatten())
                y_true.extend(target.numpy())

        y_pred_proba = np.array(y_pred_proba)
        y_true = np.array(y_true)
        y_pred = (y_pred_proba > 0.5).astype(int)

        # 获取类别名称
        class_names = self.class_names

        print(f"📊 测试集统计:")
        print(f"  总样本数: {len(y_true)}")
        print(f"  类别: {class_names}")
        for i, name in enumerate(class_names):
            count = np.sum(y_true == i)
            print(f"  {name}: {count} 个样本")

        # 1. 混淆矩阵分析
        print("\n1️⃣ 生成混淆矩阵...")
        self.plot_confusion_matrix(y_true, y_pred, class_names)

        # 2. ROC曲线和AUC分析
        print("2️⃣ 生成ROC曲线...")
        self.plot_roc_curve(y_true, y_pred_proba)

        # 3. 精确率-召回率曲线
        print("3️⃣ 生成精确率-召回率曲线...")
        self.plot_precision_recall_curve(y_true, y_pred_proba)

        # 4. 分类报告可视化
        print("4️⃣ 生成分类报告...")
        self.plot_classification_report(y_true, y_pred, class_names)

        # 5. 预测概率分布
        print("5️⃣ 生成预测概率分布...")
        self.plot_prediction_distribution(y_true, y_pred_proba, class_names)

        # 6. 特征重要性分析（SHAP或替代方法）
        print("6️⃣ 进行特征重要性分析...")
        self.shap_analysis(test_loader)

        # 7. 模型性能指标总结
        print("7️⃣ 生成性能指标总结...")
        self.plot_performance_metrics(y_true, y_pred, y_pred_proba)

        # 8. 错误分析
        print("8️⃣ 进行错误分析...")
        self.error_analysis(test_loader, y_true, y_pred, y_pred_proba)

        print("\n✅ 全面分析完成！所有图表已保存到当前目录。")

    def plot_confusion_matrix(self, y_true, y_pred, class_names):
        """绘制混淆矩阵"""
        cm = confusion_matrix(y_true, y_pred)

        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names,
                   cbar_kws={'label': '样本数量'})
        plt.title('混淆矩阵', fontsize=16, fontweight='bold')
        plt.xlabel('预测标签', fontsize=12)
        plt.ylabel('真实标签', fontsize=12)

        # 添加准确率信息
        accuracy = np.trace(cm) / np.sum(cm)
        plt.text(0.5, -0.1, f'总体准确率: {accuracy:.4f}',
                transform=plt.gca().transAxes, ha='center', fontsize=12)

        plt.tight_layout()
        plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("混淆矩阵已保存为 confusion_matrix.png")

    def plot_roc_curve(self, y_true, y_pred_proba):
        """绘制ROC曲线"""
        fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
        roc_auc = auc(fpr, tpr)

        plt.figure(figsize=(8, 6))
        plt.plot(fpr, tpr, color='darkorange', lw=2,
                label=f'ROC曲线 (AUC = {roc_auc:.4f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--',
                label='随机分类器')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('假正率 (FPR)', fontsize=12)
        plt.ylabel('真正率 (TPR)', fontsize=12)
        plt.title('ROC曲线', fontsize=16, fontweight='bold')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('roc_curve.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("ROC曲线已保存为 roc_curve.png")

    def plot_precision_recall_curve(self, y_true, y_pred_proba):
        """绘制精确率-召回率曲线"""
        precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
        pr_auc = auc(recall, precision)

        plt.figure(figsize=(8, 6))
        plt.plot(recall, precision, color='blue', lw=2,
                label=f'PR曲线 (AUC = {pr_auc:.4f})')
        plt.xlabel('召回率', fontsize=12)
        plt.ylabel('精确率', fontsize=12)
        plt.title('精确率-召回率曲线', fontsize=16, fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('precision_recall_curve.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("精确率-召回率曲线已保存为 precision_recall_curve.png")

    def plot_classification_report(self, y_true, y_pred, class_names):
        """可视化分类报告"""
        from sklearn.metrics import precision_score, recall_score, f1_score

        # 计算各项指标
        precision = precision_score(y_true, y_pred, average=None)
        recall = recall_score(y_true, y_pred, average=None)
        f1 = f1_score(y_true, y_pred, average=None)

        # 创建数据框
        metrics_df = pd.DataFrame({
            '精确率': precision,
            '召回率': recall,
            'F1分数': f1
        }, index=class_names)

        # 绘制条形图
        fig, ax = plt.subplots(figsize=(10, 6))
        metrics_df.plot(kind='bar', ax=ax, width=0.8)
        plt.title('分类性能指标', fontsize=16, fontweight='bold')
        plt.xlabel('类别', fontsize=12)
        plt.ylabel('分数', fontsize=12)
        plt.legend(title='指标')
        plt.xticks(rotation=45)
        plt.ylim(0, 1.1)

        # 添加数值标签
        for i, (idx, row) in enumerate(metrics_df.iterrows()):
            for j, value in enumerate(row):
                ax.text(i + (j-1)*0.25, value + 0.02, f'{value:.3f}',
                       ha='center', va='bottom', fontsize=10)

        plt.tight_layout()
        plt.savefig('classification_report.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("分类报告已保存为 classification_report.png")

    def plot_prediction_distribution(self, y_true, y_pred_proba, class_names):
        """绘制预测概率分布"""
        plt.figure(figsize=(12, 5))

        # 分别绘制两个类别的预测概率分布
        plt.subplot(1, 2, 1)
        for i, class_name in enumerate(class_names):
            mask = y_true == i
            plt.hist(y_pred_proba[mask], bins=30, alpha=0.7,
                    label=f'真实类别: {class_name}', density=True)
        plt.xlabel('预测概率', fontsize=12)
        plt.ylabel('密度', fontsize=12)
        plt.title('预测概率分布', fontsize=14, fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 绘制预测概率的箱线图
        plt.subplot(1, 2, 2)
        data_for_box = [y_pred_proba[y_true == i] for i in range(len(class_names))]
        plt.boxplot(data_for_box, labels=class_names)
        plt.ylabel('预测概率', fontsize=12)
        plt.title('预测概率箱线图', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('prediction_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("预测概率分布已保存为 prediction_distribution.png")

    def shap_analysis(self, test_loader):
        """SHAP分析 - 模型可解释性分析"""
        if not SHAP_AVAILABLE:
            print("SHAP库不可用，跳过SHAP分析")
            # 创建一个替代的特征重要性分析
            self.alternative_feature_analysis()
            return

        try:
            print("正在进行SHAP分析...")

            # 获取一小批测试数据用于SHAP分析
            batch_data, _ = next(iter(test_loader))
            batch_data = batch_data.to(self.device)

            # 限制样本数量以加快计算速度
            sample_size = min(10, len(batch_data))
            sample_data = batch_data[:sample_size]

            # 创建SHAP解释器
            explainer = shap.GradientExplainer(self.model, sample_data)

            # 计算SHAP值
            shap_values = explainer.shap_values(sample_data)

            # 绘制SHAP摘要图
            plt.figure(figsize=(12, 8))
            if isinstance(shap_values, list):
                shap_values = shap_values[0]

            # 获取测试数据的标签信息，选择不同类别的样本
            all_data = []
            all_labels = []
            all_shap = []

            # 收集所有数据和标签
            for i, (data, target) in enumerate(test_loader):
                if len(all_data) >= 20:  # 收集足够的样本
                    break
                data = data.to(self.device)
                batch_shap = explainer.shap_values(data)
                if isinstance(batch_shap, list):
                    batch_shap = batch_shap[0]

                all_data.extend(data.cpu())
                all_labels.extend(target.numpy())
                all_shap.extend(batch_shap)

            # 选择每个类别的样本
            class_0_indices = [i for i, label in enumerate(all_labels) if label == 0][:2]  # 癌细胞
            class_1_indices = [i for i, label in enumerate(all_labels) if label == 1][:2]  # 正常细胞

            selected_indices = class_0_indices + class_1_indices
            class_names_display = ['colon_aca', 'colon_aca', 'colon_n', 'colon_n']

            # 创建2x3的网格布局：2行，3列（原图，癌细胞SHAP，正常细胞SHAP）
            fig, axes = plt.subplots(2, 3, figsize=(15, 10))

            for idx, sample_idx in enumerate(selected_indices):
                row = idx
                if row >= 2:  # 只显示前2个样本
                    break

                # 第一列：原始图像
                original_img = all_data[sample_idx].numpy().transpose(1, 2, 0)
                # 反标准化
                mean = np.array([0.485, 0.456, 0.406])
                std = np.array([0.229, 0.224, 0.225])
                original_img = original_img * std + mean
                original_img = np.clip(original_img, 0, 1)

                axes[row, 0].imshow(original_img)
                axes[row, 0].set_title(class_names_display[idx], fontsize=14, fontweight='bold')
                axes[row, 0].axis('off')

                # 第二列：SHAP热力图（针对癌细胞类别）
                sample_shap = all_shap[sample_idx]  # 形状: (3, 224, 224)
                shap_combined = np.mean(sample_shap, axis=0)

                im1 = axes[row, 1].imshow(shap_combined, cmap='RdBu_r', alpha=0.8)
                axes[row, 1].set_title('SHAP (癌细胞)', fontsize=12, fontweight='bold')
                axes[row, 1].axis('off')

                # 第三列：SHAP热力图（针对正常细胞类别）
                # 对于二分类，正常细胞的SHAP值是癌细胞的相反
                shap_normal = -shap_combined

                im2 = axes[row, 2].imshow(shap_normal, cmap='RdBu_r', alpha=0.8)
                axes[row, 2].set_title('SHAP (正常细胞)', fontsize=12, fontweight='bold')
                axes[row, 2].axis('off')

            # 添加颜色条
            fig.colorbar(im1, ax=axes[:, 1], shrink=0.6, aspect=20, label='SHAP值 (癌细胞)')
            fig.colorbar(im2, ax=axes[:, 2], shrink=0.6, aspect=20, label='SHAP值 (正常细胞)')

            # 添加总标题
            plt.suptitle('SHAP可解释性分析 - 结肠癌细胞分类\n(红色：正向影响，蓝色：负向影响)',
                        fontsize=16, fontweight='bold', y=0.95)

            plt.tight_layout()
            plt.subplots_adjust(top=0.88)
            plt.savefig('shap_analysis.png', dpi=300, bbox_inches='tight')
            plt.show()
            print("SHAP分析已保存为 shap_analysis.png")

        except Exception as e:
            print(f"SHAP分析出现错误: {e}")
            print("跳过SHAP分析，使用替代方法...")
            self.alternative_feature_analysis()

    def alternative_feature_analysis(self):
        """替代的特征重要性分析（当SHAP不可用时）"""
        print("正在进行替代的特征重要性分析...")

        # 使用梯度分析来评估特征重要性
        try:
            # 创建一个简单的特征重要性可视化
            plt.figure(figsize=(12, 8))

            # 模拟特征重要性（基于模型层的权重）
            if hasattr(self.model, 'layers'):
                # 获取最后一个卷积层的权重
                conv_layers = [layer for layer in self.model.layers if 'conv' in layer.name.lower()]
                if conv_layers:
                    last_conv = conv_layers[-1]
                    if hasattr(last_conv, 'get_weights') and last_conv.get_weights():
                        weights = last_conv.get_weights()[0]
                        # 计算权重的平均绝对值作为重要性指标
                        importance = np.mean(np.abs(weights), axis=(0, 1, 2))

                        plt.bar(range(len(importance)), importance)
                        plt.xlabel('特征通道', fontsize=12)
                        plt.ylabel('重要性分数', fontsize=12)
                        plt.title('特征重要性分析（基于权重）', fontsize=16, fontweight='bold')
                        plt.grid(True, alpha=0.3)
                        plt.tight_layout()
                        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
                        plt.show()
                        print("特征重要性分析已保存为 feature_importance.png")
                        return

            # 如果无法获取权重，创建一个说明图
            plt.text(0.5, 0.5, '特征重要性分析\n(SHAP不可用)\n\n建议安装兼容版本的SHAP库\n进行更详细的可解释性分析',
                    ha='center', va='center', fontsize=14,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
            plt.xlim(0, 1)
            plt.ylim(0, 1)
            plt.axis('off')
            plt.title('特征重要性分析', fontsize=16, fontweight='bold')
            plt.tight_layout()
            plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
            plt.show()
            print("特征重要性说明已保存为 feature_importance.png")

        except Exception as e:
            print(f"替代特征分析也出现错误: {e}")
            print("跳过特征重要性分析")

    def plot_performance_metrics(self, y_true, y_pred, y_pred_proba):
        """绘制模型性能指标总结"""
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

        # 计算各项指标
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average='weighted')
        recall = recall_score(y_true, y_pred, average='weighted')
        f1 = f1_score(y_true, y_pred, average='weighted')

        # ROC AUC
        fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
        roc_auc = auc(fpr, tpr)

        # 创建性能指标图
        metrics = ['准确率', '精确率', '召回率', 'F1分数', 'ROC AUC']
        values = [accuracy, precision, recall, f1, roc_auc]

        plt.figure(figsize=(10, 6))
        bars = plt.bar(metrics, values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum'])
        plt.ylim(0, 1.1)
        plt.title('模型性能指标总结', fontsize=16, fontweight='bold')
        plt.ylabel('分数', fontsize=12)

        # 添加数值标签
        for bar, value in zip(bars, values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                    f'{value:.4f}', ha='center', va='bottom', fontsize=12, fontweight='bold')

        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()
        plt.savefig('performance_metrics.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("性能指标总结已保存为 performance_metrics.png")

    def error_analysis(self, test_loader, y_true, y_pred, y_pred_proba):
        """错误分析 - 分析模型预测错误的样本"""
        # 找出预测错误的样本
        wrong_predictions = y_true != y_pred
        wrong_indices = np.where(wrong_predictions)[0]

        if len(wrong_indices) == 0:
            print("模型在测试集上没有预测错误的样本！")
            return

        print(f"发现 {len(wrong_indices)} 个预测错误的样本")

        # 分析错误类型
        false_positives = np.sum((y_true == 0) & (y_pred == 1))
        false_negatives = np.sum((y_true == 1) & (y_pred == 0))

        plt.figure(figsize=(12, 5))

        # 错误类型分布
        plt.subplot(1, 2, 1)
        error_types = ['假正例\n(误诊为癌症)', '假负例\n(漏诊癌症)']
        error_counts = [false_positives, false_negatives]
        colors = ['lightcoral', 'lightsalmon']

        bars = plt.bar(error_types, error_counts, color=colors)
        plt.title('预测错误类型分析', fontsize=14, fontweight='bold')
        plt.ylabel('错误数量', fontsize=12)

        # 添加数值标签
        for bar, count in zip(bars, error_counts):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    str(count), ha='center', va='bottom', fontsize=12, fontweight='bold')

        # 错误样本的置信度分布
        plt.subplot(1, 2, 2)
        wrong_confidences = y_pred_proba[wrong_predictions]
        plt.hist(wrong_confidences, bins=20, alpha=0.7, color='orange', edgecolor='black')
        plt.xlabel('预测置信度', fontsize=12)
        plt.ylabel('错误样本数量', fontsize=12)
        plt.title('错误预测的置信度分布', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('error_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("错误分析已保存为 error_analysis.png")

        # 打印详细的错误分析报告
        print("\n=== 错误分析报告 ===")
        print(f"总测试样本数: {len(y_true)}")
        print(f"预测错误样本数: {len(wrong_indices)}")
        print(f"错误率: {len(wrong_indices)/len(y_true)*100:.2f}%")
        print(f"假正例数量: {false_positives} (误诊为癌症)")
        print(f"假负例数量: {false_negatives} (漏诊癌症)")

        if false_negatives > 0:
            print(f"⚠️  警告: 有 {false_negatives} 个癌症样本被误诊为正常，这在医学诊断中是严重问题！")

        if false_positives > 0:
            print(f"ℹ️  信息: 有 {false_positives} 个正常样本被误诊为癌症，可能导致不必要的担忧。")

def main():
    """主函数"""
    print("🔬 结肠癌细胞分类器训练程序")
    print("="*50)

    # 设置中文输出
    print("正在初始化中文支持...")

    # 检查数据集是否存在
    data_dir = 'colon_dataset_split'
    if not os.path.exists(data_dir):
        print(f"❌ 数据集目录不存在: {data_dir}")
        print("请先运行数据划分脚本")
        return

    # 检查训练和测试目录
    train_dir = os.path.join(data_dir, 'train')
    test_dir = os.path.join(data_dir, 'test')

    if not os.path.exists(train_dir):
        print(f"❌ 训练数据目录不存在: {train_dir}")
        return

    if not os.path.exists(test_dir):
        print(f"❌ 测试数据目录不存在: {test_dir}")
        return

    # 统计数据集信息
    print(f"\n📊 数据集信息:")
    for subset in ['train', 'test']:
        subset_dir = os.path.join(data_dir, subset)
        for class_name in ['colon_aca', 'colon_n']:
            class_dir = os.path.join(subset_dir, class_name)
            if os.path.exists(class_dir):
                count = len([f for f in os.listdir(class_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
                class_label = "癌细胞" if class_name == 'colon_aca' else "正常细胞"
                print(f"  {subset} - {class_label}: {count} 张图像")

    # 创建分类器实例
    classifier = ColonCancerClassifier()

    # 检查是否已有训练好的模型
    model_files = ['best_colon_cancer_model.pth', 'colon_cancer_classifier.pth']
    existing_model = None

    for model_file in model_files:
        if os.path.exists(model_file):
            existing_model = model_file
            break

    if existing_model:
        print(f"\n🔍 发现已训练的模型: {existing_model}")
        choice = input("是否使用已有模型进行分析？(y/n，默认y): ").strip().lower()

        if choice in ['', 'y', 'yes', '是']:
            print(f"📥 正在加载模型: {existing_model}")
            if classifier.load_model(existing_model):
                print("✅ 模型加载成功!")

                # 准备测试数据进行分析
                print("\n📊 准备测试数据...")
                _, test_loader = classifier.prepare_data(data_dir)

                # 进行完整分析
                print("\n🔬 开始进行模型分析...")
                classifier.comprehensive_analysis(test_loader)

                print("\n✅ 模型分析完成!")
            else:
                print("❌ 模型加载失败，将重新训练")
                existing_model = None
        else:
            print("🔄 用户选择重新训练模型")
            existing_model = None

    # 如果没有模型或用户选择重新训练
    if not existing_model:
        try:
            print(f"\n🚀 开始训练新模型...")
            classifier.train(epochs=30)
            print("\n✅ 训练和分析完成!")
        except KeyboardInterrupt:
            print("\n⚠️ 训练被用户中断")
        except Exception as e:
            print(f"\n❌ 训练过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return

    # 显示生成的分析文件
    print("\n📈 生成的分析文件:")
    analysis_files = [
        'training_history.png',
        'confusion_matrix.png',
        'roc_curve.png',
        'precision_recall_curve.png',
        'classification_report.png',
        'prediction_distribution.png',
        'shap_analysis.png',
        'feature_importance.png',
        'performance_metrics.png',
        'error_analysis.png'
    ]

    for file in analysis_files:
        if os.path.exists(file):
            print(f"  ✓ {file}")
        else:
            print(f"  ✗ {file} (未生成)")

if __name__ == "__main__":
    main()
